# JustDial Copy Backend

A comprehensive backend API for a JustDial-like service platform with user management, tenant services, staff system, and referral functionality.

## Features

### Core Functionality
- **User Management**: Registration, login, location-based services
- **Tenant Management**: Business registration, service management, wallet system
- **Staff System**: Tenant staff management with referral capabilities
- **Location Services**: Nearby service discovery
- **Referral System**: Multi-level referral system (User → User, Tenant → Tenant, Staff → User/Tenant)
- **File Upload**: Service image management (max 10 images per service)
- **Security**: Rate limiting, authentication, data validation

### User Types
1. **Users**: End customers who search for services
2. **Tenants**: Business owners who provide services
3. **Staff**: Employees of tenants who can refer customers

## Technology Stack

- **Runtime**: Node.js with Express.js
- **Database**: MySQL with Sequelize ORM
- **Authentication**: JWT tokens with bcryptjs password hashing
- **Security**: Helmet, CORS, Rate limiting, Input validation
- **File Upload**: Multer for image handling
- **Caching**: Node-cache for performance
- **Logging**: <PERSON> with Morgan for HTTP logging

## Database Schema

### Users Table
- Basic user information with location data
- Referral code system
- Location enable/disable functionality

### Tenants Table
- Business information and location
- Wallet system for payments
- Rating and review system
- Referral capabilities

### Staff Table (NEW)
- Employee management for tenants
- Individual referral codes for staff
- Commission tracking
- Department and designation management

### Services Table
- Service offerings by tenants
- Category-based organization
- Pricing and availability management
- Image gallery support

### Referrals Table
- Multi-type referral tracking (user/tenant/staff)
- Commission calculation and status
- Referral code validation

## API Endpoints

### Authentication
```
POST /api/users/register     - User registration
POST /api/users/login        - User login
POST /api/tenants/register   - Tenant registration
POST /api/tenants/login      - Tenant login
POST /api/staff/login        - Staff login
```

### User Management
```
GET  /api/users/profile      - Get user profile
PUT  /api/users/profile      - Update user profile
PUT  /api/users/location     - Update user location
GET  /api/users/search       - Search nearby services
```

### Tenant Management
```
GET  /api/tenants/profile    - Get tenant profile
PUT  /api/tenants/profile    - Update tenant profile
GET  /api/tenants/services   - Get tenant services
POST /api/tenants/services   - Create new service
PUT  /api/tenants/services/:id - Update service
```

### Staff Management (NEW)
```
POST /api/staff              - Create staff (by tenant)
GET  /api/staff              - Get tenant's staff list
PUT  /api/staff/:id          - Update staff details
GET  /api/staff/profile      - Get staff profile
GET  /api/staff/referrals    - Get staff referrals
```

### Service Management
```
POST /api/services/:id/images - Upload service images
GET  /api/services/search     - Search services
GET  /api/services/:id        - Get service details
```

### Referral System
```
GET  /api/referrals          - Get user/tenant/staff referrals
POST /api/referrals/validate - Validate referral code
```

## Staff Referral System

### How It Works
1. **Staff Creation**: Tenants can create staff accounts with referral capabilities
2. **Referral Codes**: Each staff member gets a unique referral code (starts with 'S')
3. **Commission Structure**: 
   - Staff get lower commission (2.5% default) compared to direct tenant referrals (5%)
   - Commissions are tracked separately for each staff member
4. **Referral Types**: Staff can refer both users and tenants
5. **Tracking**: All referrals are tracked with commission calculations

### Staff Features
- Individual login system
- Referral code management
- Commission tracking
- Department/designation management
- Activity monitoring by tenant

## Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd justdial-copy-backend
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Setup**
```bash
cp .env.example .env
# Edit .env with your database and other configurations
```

4. **Database Setup**
```bash
# Create MySQL database
mysql -u root -p
CREATE DATABASE justdial_copy;

# Run migrations (if using Sequelize CLI)
npm run migrate
```

5. **Start the server**
```bash
# Development
npm run dev

# Production
npm start
```

## Environment Variables

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=justdial_copy
DB_USER=root
DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRES_IN=7d

# Referral Configuration
REFERRAL_COMMISSION_PERCENTAGE=5
STAFF_COMMISSION_PERCENTAGE=2.5
MIN_REFERRAL_AMOUNT=100

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Security
BCRYPT_SALT_ROUNDS=12
```

## Security Features

- **Password Hashing**: bcryptjs with configurable salt rounds
- **Rate Limiting**: Different limits for auth, upload, and general endpoints
- **Input Validation**: Joi validation for all inputs
- **CORS Protection**: Configurable CORS policies
- **Helmet**: Security headers
- **JWT Authentication**: Secure token-based authentication

## File Structure

```
src/
├── config/          # Database and logger configuration
├── controllers/     # Route controllers
├── middleware/      # Authentication, rate limiting, upload
├── models/          # Sequelize models
├── routes/          # API routes
├── services/        # Business logic services
├── utils/           # Utility functions
└── validations/     # Input validation schemas
```

## Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License
