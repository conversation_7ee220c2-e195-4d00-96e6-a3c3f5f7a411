{"info": {"name": "JustDial Copy API", "description": "Complete API collection for JustDial Copy Backend with User, Tenant, and Staff management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api", "type": "string"}, {"key": "userToken", "value": "", "type": "string"}, {"key": "tenantToken", "value": "", "type": "string"}, {"key": "staffToken", "value": "", "type": "string"}], "item": [{"name": "User Management", "item": [{"name": "User Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123\",\n  \"phone\": \"9876543210\",\n  \"referral_code\": \"ABC12345\"\n}"}, "url": {"raw": "{{baseUrl}}/users/register", "host": ["{{baseUrl}}"], "path": ["users", "register"]}}}, {"name": "User Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123\"\n}"}, "url": {"raw": "{{baseUrl}}/users/login", "host": ["{{baseUrl}}"], "path": ["users", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('userToken', response.data.token);", "}"]}}]}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{userToken}}"}], "url": {"raw": "{{baseUrl}}/users/profile", "host": ["{{baseUrl}}"], "path": ["users", "profile"]}}}, {"name": "Update User Location", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{userToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"latitude\": 28.6139,\n  \"longitude\": 77.2090,\n  \"address\": \"New Delhi, India\",\n  \"city\": \"New Delhi\",\n  \"state\": \"Delhi\",\n  \"pincode\": \"110001\"\n}"}, "url": {"raw": "{{baseUrl}}/users/location", "host": ["{{baseUrl}}"], "path": ["users", "location"]}}}]}, {"name": "Tenant Management", "item": [{"name": "Tenant Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Business Owner\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123\",\n  \"phone\": \"9876543211\",\n  \"business_name\": \"Tech Solutions\",\n  \"business_description\": \"IT Services and Solutions\",\n  \"business_category\": \"Technology\",\n  \"latitude\": 28.6139,\n  \"longitude\": 77.2090,\n  \"address\": \"Business District, New Delhi\",\n  \"city\": \"New Delhi\",\n  \"state\": \"Delhi\",\n  \"pincode\": \"110001\"\n}"}, "url": {"raw": "{{baseUrl}}/tenants/register", "host": ["{{baseUrl}}"], "path": ["tenants", "register"]}}}, {"name": "Tenant <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123\"\n}"}, "url": {"raw": "{{baseUrl}}/tenants/login", "host": ["{{baseUrl}}"], "path": ["tenants", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('tenantToken', response.data.token);", "}"]}}]}, {"name": "Get Tenant Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{tenantToken}}"}], "url": {"raw": "{{baseUrl}}/tenants/profile", "host": ["{{baseUrl}}"], "path": ["tenants", "profile"]}}}]}, {"name": "Staff Management", "item": [{"name": "Create Staff (by Tenant)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{tenantToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Staff Member\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123\",\n  \"phone\": \"9876543212\",\n  \"designation\": \"Sales Executive\",\n  \"department\": \"Sales\",\n  \"commission_percentage\": 3.0,\n  \"can_refer\": true,\n  \"notes\": \"Experienced sales professional\"\n}"}, "url": {"raw": "{{baseUrl}}/staff", "host": ["{{baseUrl}}"], "path": ["staff"]}}}, {"name": "Staff Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123\"\n}"}, "url": {"raw": "{{baseUrl}}/staff/login", "host": ["{{baseUrl}}"], "path": ["staff", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('staffToken', response.data.token);", "}"]}}]}, {"name": "Get Staff Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{staffToken}}"}], "url": {"raw": "{{baseUrl}}/staff/profile", "host": ["{{baseUrl}}"], "path": ["staff", "profile"]}}}, {"name": "Get Tenant Staff List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{tenantToken}}"}], "url": {"raw": "{{baseUrl}}/staff?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["staff"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Update Staff", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{tenantToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"designation\": \"Senior Sales Executive\",\n  \"commission_percentage\": 3.5,\n  \"notes\": \"Promoted to senior position\"\n}"}, "url": {"raw": "{{baseUrl}}/staff/1", "host": ["{{baseUrl}}"], "path": ["staff", "1"]}}}, {"name": "Get Staff Referrals", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{staffToken}}"}], "url": {"raw": "{{baseUrl}}/staff/referrals?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["staff", "referrals"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}]}]}