const express = require('express');
const router = express.Router();
const { 
  createStaff,
  staffLogin,
  getStaffProfile,
  getTenantStaff,
  updateStaff,
  getStaffReferrals
} = require('../controllers/staffController');
const { 
  authenticateTenant, 
  authenticateStaff 
} = require('../middleware/auth');
const { authLimiter } = require('../middleware/rateLimiter');

// Staff authentication routes
router.post('/login', authLimiter, staffLogin);

// Staff profile routes (requires staff authentication)
router.get('/profile', authenticateStaff, getStaffProfile);
router.get('/referrals', authenticateStaff, getStaffReferrals);

// Tenant staff management routes (requires tenant authentication)
router.post('/', authenticateTenant, createStaff);
router.get('/', authenticateTenant, getTenantStaff);
router.put('/:staffId', authenticateTenant, updateStaff);

module.exports = router;
