const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ServiceImage = sequelize.define('ServiceImage', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  service_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'services',
      key: 'id'
    }
  },
  image_url: {
    type: DataTypes.STRING(500),
    allowNull: false,
    validate: {
      notEmpty: true,
      isUrl: true
    }
  },
  image_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  image_size: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  image_type: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  is_primary: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  display_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  tableName: 'service_images',
  indexes: [
    { fields: ['service_id'] },
    { fields: ['is_primary'] },
    { fields: ['display_order'] },
    { fields: ['is_active'] }
  ]
});

module.exports = ServiceImage;
