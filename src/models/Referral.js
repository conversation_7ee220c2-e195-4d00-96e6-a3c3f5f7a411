const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Referral = sequelize.define('Referral', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  referrer_id: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  referrer_type: {
    type: DataTypes.ENUM('user', 'tenant', 'staff'),
    allowNull: false
  },
  referred_id: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  referred_type: {
    type: DataTypes.ENUM('user', 'tenant', 'staff'),
    allowNull: false
  },
  referral_code: {
    type: DataTypes.STRING(10),
    allowNull: false
  },
  commission_amount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00,
    validate: {
      min: 0
    }
  },
  commission_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 5.00,
    validate: {
      min: 0,
      max: 100
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'completed', 'cancelled'),
    defaultValue: 'pending'
  },
  completed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'referrals',
  indexes: [
    { fields: ['referrer_id', 'referrer_type'] },
    { fields: ['referred_id', 'referred_type'] },
    { fields: ['referral_code'] },
    { fields: ['status'] }
  ]
});

module.exports = Referral;
