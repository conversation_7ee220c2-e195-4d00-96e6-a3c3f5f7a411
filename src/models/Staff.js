const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');

const Staff = sequelize.define('Staff', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  tenant_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'tenants',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 100]
    }
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      len: [6, 255]
    }
  },
  phone: {
    type: DataTypes.STRING(15),
    allowNull: false,
    unique: true,
    validate: {
      is: /^[6-9]\d{9}$/ // Indian phone number format
    }
  },
  employee_id: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true
  },
  designation: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  department: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  referral_code: {
    type: DataTypes.STRING(10),
    allowNull: false,
    unique: true
  },
  commission_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 2.50, // Staff gets lower commission than direct tenant referrals
    validate: {
      min: 0,
      max: 100
    }
  },
  total_referrals: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  total_commission_earned: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00,
    validate: {
      min: 0
    }
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  can_refer: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  joining_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  last_login: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'staff',
  indexes: [
    { fields: ['tenant_id'] },
    { fields: ['email'] },
    { fields: ['phone'] },
    { fields: ['employee_id'] },
    { fields: ['referral_code'] },
    { fields: ['is_active'] },
    { fields: ['can_refer'] }
  ]
});

// Hash password before saving
Staff.beforeCreate(async (staff) => {
  if (staff.password) {
    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
    staff.password = await bcrypt.hash(staff.password, saltRounds);
  }
  
  // Generate unique referral code
  if (!staff.referral_code) {
    staff.referral_code = generateStaffReferralCode();
  }
  
  // Generate employee ID if not provided
  if (!staff.employee_id) {
    staff.employee_id = await generateEmployeeId(staff.tenant_id);
  }
});

Staff.beforeUpdate(async (staff) => {
  if (staff.changed('password')) {
    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
    staff.password = await bcrypt.hash(staff.password, saltRounds);
  }
});

// Instance methods
Staff.prototype.validatePassword = async function(password) {
  return await bcrypt.compare(password, this.password);
};

Staff.prototype.toJSON = function() {
  const values = { ...this.get() };
  delete values.password;
  return values;
};

// Helper function to generate staff referral code
function generateStaffReferralCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = 'S'; // S for Staff
  for (let i = 0; i < 7; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Helper function to generate employee ID
async function generateEmployeeId(tenantId) {
  const staffCount = await Staff.count({ where: { tenant_id: tenantId } });
  const paddedCount = String(staffCount + 1).padStart(4, '0');
  return `EMP${tenantId}${paddedCount}`;
}

module.exports = Staff;
