const { sequelize } = require('../config/database');
const User = require('./User');
const Tenant = require('./Tenant');
const Service = require('./Service');
const ServiceImage = require('./ServiceImage');
const Referral = require('./Referral');

// Define associations
// User associations
User.hasMany(Referral, { 
  foreignKey: 'referrer_id',
  constraints: false,
  scope: { referrer_type: 'user' },
  as: 'referrals_made'
});

User.hasMany(Referral, { 
  foreignKey: 'referred_id',
  constraints: false,
  scope: { referred_type: 'user' },
  as: 'referrals_received'
});

User.belongsTo(User, { 
  foreignKey: 'referred_by',
  as: 'referrer'
});

// Tenant associations
Tenant.hasMany(Service, { 
  foreignKey: 'tenant_id',
  as: 'services'
});

Tenant.hasMany(Referral, { 
  foreignKey: 'referrer_id',
  constraints: false,
  scope: { referrer_type: 'tenant' },
  as: 'referrals_made'
});

Tenant.hasMany(Referral, { 
  foreignKey: 'referred_id',
  constraints: false,
  scope: { referred_type: 'tenant' },
  as: 'referrals_received'
});

Tenant.belongsTo(Tenant, { 
  foreignKey: 'referred_by',
  as: 'referrer'
});

// Service associations
Service.belongsTo(Tenant, { 
  foreignKey: 'tenant_id',
  as: 'tenant'
});

Service.hasMany(ServiceImage, { 
  foreignKey: 'service_id',
  as: 'images'
});

// ServiceImage associations
ServiceImage.belongsTo(Service, { 
  foreignKey: 'service_id',
  as: 'service'
});

// Referral associations
Referral.belongsTo(User, { 
  foreignKey: 'referrer_id',
  constraints: false,
  as: 'referrer_user'
});

Referral.belongsTo(Tenant, { 
  foreignKey: 'referrer_id',
  constraints: false,
  as: 'referrer_tenant'
});

Referral.belongsTo(User, { 
  foreignKey: 'referred_id',
  constraints: false,
  as: 'referred_user'
});

Referral.belongsTo(Tenant, { 
  foreignKey: 'referred_id',
  constraints: false,
  as: 'referred_tenant'
});

// Sync database
const syncDatabase = async (force = false) => {
  try {
    await sequelize.sync({ force });
    console.log('✅ Database synchronized successfully.');
  } catch (error) {
    console.error('❌ Error synchronizing database:', error);
    throw error;
  }
};

module.exports = {
  sequelize,
  User,
  Tenant,
  Service,
  ServiceImage,
  Referral,
  syncDatabase
};
