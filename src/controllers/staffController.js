const jwt = require('jsonwebtoken');
const { Staff, Tenant, User, Referral } = require('../models');
const logger = require('../config/logger');
const { 
  createStaffValidation, 
  staffLoginValidation, 
  updateStaffValidation,
  staffReferralValidation 
} = require('../validations/staffValidation');

// Create staff (by tenant)
const createStaff = async (req, res) => {
  try {
    const { error, value } = createStaffValidation.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    // Check if email or phone already exists
    const existingStaff = await Staff.findOne({
      where: {
        $or: [
          { email: value.email },
          { phone: value.phone }
        ]
      }
    });

    if (existingStaff) {
      return res.status(409).json({
        success: false,
        message: 'Staff with this email or phone already exists'
      });
    }

    // Create staff
    const staff = await Staff.create({
      ...value,
      tenant_id: req.currentTenant.id
    });

    logger.info(`Staff created: ${staff.id} by tenant: ${req.currentTenant.id}`);

    res.status(201).json({
      success: true,
      message: 'Staff created successfully',
      data: {
        staff: {
          id: staff.id,
          name: staff.name,
          email: staff.email,
          phone: staff.phone,
          employee_id: staff.employee_id,
          designation: staff.designation,
          department: staff.department,
          referral_code: staff.referral_code,
          commission_percentage: staff.commission_percentage,
          can_refer: staff.can_refer,
          is_active: staff.is_active
        }
      }
    });
  } catch (error) {
    logger.error('Error creating staff:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Staff login
const staffLogin = async (req, res) => {
  try {
    const { error, value } = staffLoginValidation.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { email, password } = value;

    // Find staff with tenant details
    const staff = await Staff.findOne({
      where: { email },
      include: [{
        model: Tenant,
        as: 'tenant'
      }]
    });

    if (!staff || !staff.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials or inactive account'
      });
    }

    // Validate password
    const isValidPassword = await staff.validatePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Update last login
    await staff.update({ last_login: new Date() });

    // Generate JWT token
    const token = jwt.sign(
      { 
        id: staff.id, 
        type: 'staff',
        tenant_id: staff.tenant_id
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    logger.info(`Staff login: ${staff.id}`);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        token,
        staff: {
          id: staff.id,
          name: staff.name,
          email: staff.email,
          phone: staff.phone,
          employee_id: staff.employee_id,
          designation: staff.designation,
          department: staff.department,
          referral_code: staff.referral_code,
          commission_percentage: staff.commission_percentage,
          can_refer: staff.can_refer,
          tenant: {
            id: staff.tenant.id,
            business_name: staff.tenant.business_name,
            business_category: staff.tenant.business_category
          }
        }
      }
    });
  } catch (error) {
    logger.error('Error in staff login:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get staff profile
const getStaffProfile = async (req, res) => {
  try {
    const staff = await Staff.findByPk(req.currentStaff.id, {
      include: [{
        model: Tenant,
        as: 'tenant'
      }]
    });

    res.json({
      success: true,
      data: {
        staff: {
          id: staff.id,
          name: staff.name,
          email: staff.email,
          phone: staff.phone,
          employee_id: staff.employee_id,
          designation: staff.designation,
          department: staff.department,
          referral_code: staff.referral_code,
          commission_percentage: staff.commission_percentage,
          total_referrals: staff.total_referrals,
          total_commission_earned: staff.total_commission_earned,
          can_refer: staff.can_refer,
          joining_date: staff.joining_date,
          tenant: {
            id: staff.tenant.id,
            business_name: staff.tenant.business_name,
            business_category: staff.tenant.business_category
          }
        }
      }
    });
  } catch (error) {
    logger.error('Error getting staff profile:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get tenant's staff list
const getTenantStaff = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const { count, rows: staff } = await Staff.findAndCountAll({
      where: { tenant_id: req.currentTenant.id },
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        staff,
        pagination: {
          current_page: page,
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: limit
        }
      }
    });
  } catch (error) {
    logger.error('Error getting tenant staff:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Update staff (by tenant)
const updateStaff = async (req, res) => {
  try {
    const { staffId } = req.params;
    const { error, value } = updateStaffValidation.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const staff = await Staff.findOne({
      where: { 
        id: staffId,
        tenant_id: req.currentTenant.id 
      }
    });

    if (!staff) {
      return res.status(404).json({
        success: false,
        message: 'Staff not found'
      });
    }

    await staff.update(value);

    logger.info(`Staff updated: ${staffId} by tenant: ${req.currentTenant.id}`);

    res.json({
      success: true,
      message: 'Staff updated successfully',
      data: { staff }
    });
  } catch (error) {
    logger.error('Error updating staff:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get staff referrals
const getStaffReferrals = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const { count, rows: referrals } = await Referral.findAndCountAll({
      where: { 
        referrer_id: req.currentStaff.id,
        referrer_type: 'staff'
      },
      include: [
        {
          model: User,
          as: 'referred_user',
          required: false
        },
        {
          model: Tenant,
          as: 'referred_tenant',
          required: false
        }
      ],
      limit,
      offset,
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        referrals,
        pagination: {
          current_page: page,
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: limit
        }
      }
    });
  } catch (error) {
    logger.error('Error getting staff referrals:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  createStaff,
  staffLogin,
  getStaffProfile,
  getTenantStaff,
  updateStaff,
  getStaffReferrals
};
