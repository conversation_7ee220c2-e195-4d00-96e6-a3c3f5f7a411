const jwt = require('jsonwebtoken');
const { User, Tenant, Staff } = require('../models');
const logger = require('../config/logger');

// Verify JWT token
const verifyToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access token is required'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    logger.error('Token verification failed:', error);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token has expired'
      });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    
    return res.status(401).json({
      success: false,
      message: 'Token verification failed'
    });
  }
};

// Middleware for user authentication
const authenticateUser = async (req, res, next) => {
  try {
    verifyToken(req, res, async () => {
      if (req.user.type !== 'user') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. User authentication required.'
        });
      }

      const user = await User.findByPk(req.user.id);
      if (!user || !user.is_active) {
        return res.status(401).json({
          success: false,
          message: 'User not found or inactive'
        });
      }

      req.currentUser = user;
      next();
    });
  } catch (error) {
    logger.error('User authentication failed:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication failed'
    });
  }
};

// Middleware for tenant authentication
const authenticateTenant = async (req, res, next) => {
  try {
    verifyToken(req, res, async () => {
      if (req.user.type !== 'tenant') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Tenant authentication required.'
        });
      }

      const tenant = await Tenant.findByPk(req.user.id);
      if (!tenant || !tenant.is_active) {
        return res.status(401).json({
          success: false,
          message: 'Tenant not found or inactive'
        });
      }

      req.currentTenant = tenant;
      next();
    });
  } catch (error) {
    logger.error('Tenant authentication failed:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication failed'
    });
  }
};

// Middleware for staff authentication
const authenticateStaff = async (req, res, next) => {
  try {
    verifyToken(req, res, async () => {
      if (req.user.type !== 'staff') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Staff authentication required.'
        });
      }

      const staff = await Staff.findByPk(req.user.id, {
        include: [{
          model: Tenant,
          as: 'tenant'
        }]
      });

      if (!staff || !staff.is_active) {
        return res.status(401).json({
          success: false,
          message: 'Staff not found or inactive'
        });
      }

      req.currentStaff = staff;
      next();
    });
  } catch (error) {
    logger.error('Staff authentication failed:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication failed'
    });
  }
};

// Middleware for user, tenant, and staff authentication
const authenticateAny = async (req, res, next) => {
  try {
    verifyToken(req, res, async () => {
      if (req.user.type === 'user') {
        const user = await User.findByPk(req.user.id);
        if (!user || !user.is_active) {
          return res.status(401).json({
            success: false,
            message: 'User not found or inactive'
          });
        }
        req.currentUser = user;
      } else if (req.user.type === 'tenant') {
        const tenant = await Tenant.findByPk(req.user.id);
        if (!tenant || !tenant.is_active) {
          return res.status(401).json({
            success: false,
            message: 'Tenant not found or inactive'
          });
        }
        req.currentTenant = tenant;
      } else if (req.user.type === 'staff') {
        const staff = await Staff.findByPk(req.user.id, {
          include: [{
            model: Tenant,
            as: 'tenant'
          }]
        });
        if (!staff || !staff.is_active) {
          return res.status(401).json({
            success: false,
            message: 'Staff not found or inactive'
          });
        }
        req.currentStaff = staff;
      } else {
        return res.status(403).json({
          success: false,
          message: 'Invalid user type'
        });
      }

      next();
    });
  } catch (error) {
    logger.error('Authentication failed:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication failed'
    });
  }
};

module.exports = {
  verifyToken,
  authenticateUser,
  authenticateTenant,
  authenticateStaff,
  authenticateAny
};
