const Joi = require('joi');

// Staff registration validation (by tenant)
const createStaffValidation = Joi.object({
  name: Joi.string()
    .min(2)
    .max(100)
    .required()
    .messages({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name cannot exceed 100 characters',
      'any.required': 'Name is required'
    }),
  
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  
  password: Joi.string()
    .min(6)
    .max(50)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)'))
    .required()
    .messages({
      'string.min': 'Password must be at least 6 characters long',
      'string.max': 'Password cannot exceed 50 characters',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
      'any.required': 'Password is required'
    }),
  
  phone: Joi.string()
    .pattern(/^[6-9]\d{9}$/)
    .required()
    .messages({
      'string.pattern.base': 'Please provide a valid Indian phone number',
      'any.required': 'Phone number is required'
    }),
  
  designation: Joi.string()
    .min(2)
    .max(100)
    .required()
    .messages({
      'string.min': 'Designation must be at least 2 characters long',
      'string.max': 'Designation cannot exceed 100 characters',
      'any.required': 'Designation is required'
    }),
  
  department: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Department cannot exceed 100 characters'
    }),
  
  commission_percentage: Joi.number()
    .min(0)
    .max(10)
    .optional()
    .default(2.5)
    .messages({
      'number.min': 'Commission percentage cannot be negative',
      'number.max': 'Commission percentage cannot exceed 10%'
    }),
  
  can_refer: Joi.boolean()
    .optional()
    .default(true),
  
  notes: Joi.string()
    .max(500)
    .optional()
    .messages({
      'string.max': 'Notes cannot exceed 500 characters'
    })
});

// Staff login validation
const staffLoginValidation = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  
  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required'
    })
});

// Update staff validation
const updateStaffValidation = Joi.object({
  name: Joi.string()
    .min(2)
    .max(100)
    .optional()
    .messages({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name cannot exceed 100 characters'
    }),
  
  phone: Joi.string()
    .pattern(/^[6-9]\d{9}$/)
    .optional()
    .messages({
      'string.pattern.base': 'Please provide a valid Indian phone number'
    }),
  
  designation: Joi.string()
    .min(2)
    .max(100)
    .optional()
    .messages({
      'string.min': 'Designation must be at least 2 characters long',
      'string.max': 'Designation cannot exceed 100 characters'
    }),
  
  department: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Department cannot exceed 100 characters'
    }),
  
  commission_percentage: Joi.number()
    .min(0)
    .max(10)
    .optional()
    .messages({
      'number.min': 'Commission percentage cannot be negative',
      'number.max': 'Commission percentage cannot exceed 10%'
    }),
  
  can_refer: Joi.boolean()
    .optional(),
  
  is_active: Joi.boolean()
    .optional(),
  
  notes: Joi.string()
    .max(500)
    .optional()
    .messages({
      'string.max': 'Notes cannot exceed 500 characters'
    })
});

// Staff referral validation
const staffReferralValidation = Joi.object({
  referral_code: Joi.string()
    .length(8)
    .required()
    .messages({
      'string.length': 'Referral code must be exactly 8 characters',
      'any.required': 'Referral code is required'
    }),
  
  referred_type: Joi.string()
    .valid('user', 'tenant')
    .required()
    .messages({
      'any.only': 'Referred type must be either user or tenant',
      'any.required': 'Referred type is required'
    }),
  
  referred_email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Referred email is required'
    })
});

module.exports = {
  createStaffValidation,
  staffLoginValidation,
  updateStaffValidation,
  staffReferralValidation
};
