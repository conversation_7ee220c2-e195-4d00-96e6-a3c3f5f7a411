{"name": "justdial-copy-backend", "version": "1.0.0", "description": "JustDial Copy Backend API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "migrate": "npx sequelize-cli db:migrate", "seed": "npx sequelize-cli db:seed:all"}, "keywords": ["justdial", "backend", "api", "nodejs"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "sequelize": "^6.35.2", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "node-cache": "^5.1.2", "multer": "^1.4.5-lts.1", "joi": "^17.11.0", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "compression": "^1.7.4", "express-slow-down": "^2.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "sequelize-cli": "^6.6.2"}, "engines": {"node": ">=16.0.0"}}